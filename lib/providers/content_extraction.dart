import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/service/content_extraction/page_content_extraction_service.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Provider for the main content extraction service
final contentExtractionServiceProvider = Provider<PageContentExtractionService>((ref) {
  return PageContentExtractionService();
});

/// State class for content extraction
class ContentExtractionState {
  final bool isExtracting;
  final ExtractionResult? result;
  final ExtractionProgress? progress;
  final String? error;
  final Map<int, ExtractionResult> cachedResults;
  final Map<String, PageContent> cachedPages;

  const ContentExtractionState({
    this.isExtracting = false,
    this.result,
    this.progress,
    this.error,
    this.cachedResults = const {},
    this.cachedPages = const {},
  });

  ContentExtractionState copyWith({
    bool? isExtracting,
    ExtractionResult? result,
    ExtractionProgress? progress,
    String? error,
    Map<int, ExtractionResult>? cachedResults,
    Map<String, PageContent>? cachedPages,
  }) {
    return ContentExtractionState(
      isExtracting: isExtracting ?? this.isExtracting,
      result: result ?? this.result,
      progress: progress ?? this.progress,
      error: error ?? this.error,
      cachedResults: cachedResults ?? this.cachedResults,
      cachedPages: cachedPages ?? this.cachedPages,
    );
  }
}

/// Notifier for content extraction state
class ContentExtractionNotifier extends StateNotifier<ContentExtractionState> {
  final PageContentExtractionService _service;
  StreamSubscription<ExtractionProgress>? _progressSubscription;

  ContentExtractionNotifier(this._service) : super(const ContentExtractionState());

  /// Extract content from a book
  Future<ExtractionResult> extractContent(
    Book book, {
    ExtractionConfig? config,
  }) async {
    if (state.isExtracting) {
      throw StateError('Extraction already in progress');
    }

    // Check if we have cached result
    final cachedResult = state.cachedResults[book.id];
    if (cachedResult != null && cachedResult.success) {
      AnxLog.info('Using cached extraction result for book: ${book.title}');
      state = state.copyWith(result: cachedResult);
      return cachedResult;
    }

    state = state.copyWith(
      isExtracting: true,
      error: null,
      progress: null,
    );

    try {
      // Subscribe to progress updates
      final progressStream = _service.getExtractionProgress(book);
      _progressSubscription = progressStream.listen(
        (progress) {
          state = state.copyWith(progress: progress);
        },
        onError: (error) {
          AnxLog.warning('Progress stream error: $error');
        },
      );

      final result = await _service.extractContent(
        book,
        config: config,
        progressStream: progressStream,
      );

      // Cache the result if successful
      if (result.success) {
        final updatedCache = Map<int, ExtractionResult>.from(state.cachedResults);
        updatedCache[book.id] = result;
        
        state = state.copyWith(
          isExtracting: false,
          result: result,
          progress: null,
          cachedResults: updatedCache,
        );
      } else {
        state = state.copyWith(
          isExtracting: false,
          result: result,
          progress: null,
          error: result.errorMessage,
        );
      }

      return result;
    } catch (e) {
      final errorMessage = 'Content extraction failed: $e';
      AnxLog.severe(errorMessage);
      
      state = state.copyWith(
        isExtracting: false,
        error: errorMessage,
        progress: null,
      );
      
      return ExtractionResult(
        success: false,
        errorMessage: errorMessage,
        startTime: DateTime.now(),
      );
    } finally {
      await _progressSubscription?.cancel();
      _progressSubscription = null;
    }
  }

  /// Extract content from a specific page
  Future<PageContent?> extractPageContent(
    Book book,
    String pageId, {
    ExtractionConfig? config,
  }) async {
    try {
      final cacheKey = '${book.id}_$pageId';
      
      // Check cache first
      final cachedPage = state.cachedPages[cacheKey];
      if (cachedPage != null) {
        AnxLog.info('Using cached page content: $pageId');
        return cachedPage;
      }

      final pageContent = await _service.extractPageContent(
        book,
        pageId,
        config: config,
      );

      // Cache the page content
      if (pageContent != null) {
        final updatedCache = Map<String, PageContent>.from(state.cachedPages);
        updatedCache[cacheKey] = pageContent;
        
        state = state.copyWith(cachedPages: updatedCache);
      }

      return pageContent;
    } catch (e) {
      AnxLog.severe('Failed to extract page content: $e');
      return null;
    }
  }

  /// Cancel ongoing extraction
  Future<void> cancelExtraction(Book book) async {
    if (!state.isExtracting) return;

    try {
      await _service.cancelExtraction(book);
      await _progressSubscription?.cancel();
      _progressSubscription = null;
      
      state = state.copyWith(
        isExtracting: false,
        progress: null,
        error: 'Extraction cancelled',
      );
      
      AnxLog.info('Content extraction cancelled for book: ${book.title}');
    } catch (e) {
      AnxLog.warning('Error cancelling extraction: $e');
    }
  }

  /// Clear cached content for a book
  Future<void> clearBookCache(int bookId) async {
    try {
      await _service.clearCache(bookId);
      
      // Remove from local cache
      final updatedResultCache = Map<int, ExtractionResult>.from(state.cachedResults);
      updatedResultCache.remove(bookId);
      
      final updatedPageCache = Map<String, PageContent>.from(state.cachedPages);
      updatedPageCache.removeWhere((key, value) => key.startsWith('${bookId}_'));
      
      state = state.copyWith(
        cachedResults: updatedResultCache,
        cachedPages: updatedPageCache,
      );
      
      AnxLog.info('Cleared cache for book: $bookId');
    } catch (e) {
      AnxLog.warning('Error clearing book cache: $e');
    }
  }

  /// Clear all cached content
  Future<void> clearAllCache() async {
    try {
      await _service.clearAllCache();
      
      state = state.copyWith(
        cachedResults: {},
        cachedPages: {},
      );
      
      AnxLog.info('Cleared all content extraction cache');
    } catch (e) {
      AnxLog.warning('Error clearing all cache: $e');
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStatistics() async {
    try {
      final serviceStats = await _service.getCacheStatistics();
      final localStats = {
        'localCachedResults': state.cachedResults.length,
        'localCachedPages': state.cachedPages.length,
      };
      
      return {...serviceStats, ...localStats};
    } catch (e) {
      AnxLog.warning('Error getting cache statistics: $e');
      return {'error': e.toString()};
    }
  }

  /// Reset state
  void reset() {
    state = const ContentExtractionState();
  }

  @override
  void dispose() {
    _progressSubscription?.cancel();
    super.dispose();
  }
}

/// Provider for content extraction state
final contentExtractionProvider = StateNotifierProvider<ContentExtractionNotifier, ContentExtractionState>((ref) {
  final service = ref.watch(contentExtractionServiceProvider);
  return ContentExtractionNotifier(service);
});

/// Provider for extraction progress
final extractionProgressProvider = Provider<ExtractionProgress?>((ref) {
  return ref.watch(contentExtractionProvider).progress;
});

/// Provider for checking if extraction is in progress
final isExtractingProvider = Provider<bool>((ref) {
  return ref.watch(contentExtractionProvider).isExtracting;
});

/// Provider for extraction result
final extractionResultProvider = Provider<ExtractionResult?>((ref) {
  return ref.watch(contentExtractionProvider).result;
});

/// Provider for extraction error
final extractionErrorProvider = Provider<String?>((ref) {
  return ref.watch(contentExtractionProvider).error;
});

/// Provider for cached extraction results
final cachedExtractionResultsProvider = Provider<Map<int, ExtractionResult>>((ref) {
  return ref.watch(contentExtractionProvider).cachedResults;
});

/// Provider for cached page contents
final cachedPageContentsProvider = Provider<Map<String, PageContent>>((ref) {
  return ref.watch(contentExtractionProvider).cachedPages;
});

/// Provider for getting extraction result for a specific book
final bookExtractionResultProvider = Provider.family<ExtractionResult?, int>((ref, bookId) {
  final cachedResults = ref.watch(cachedExtractionResultsProvider);
  return cachedResults[bookId];
});

/// Provider for getting page content for a specific book and page
final pageContentProvider = Provider.family<PageContent?, String>((ref, cacheKey) {
  final cachedPages = ref.watch(cachedPageContentsProvider);
  return cachedPages[cacheKey];
});

/// Provider for extraction statistics
final extractionStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final notifier = ref.watch(contentExtractionProvider.notifier);
  return await notifier.getCacheStatistics();
});

/// Helper function to generate cache key for page content
String generatePageCacheKey(int bookId, String pageId) {
  return '${bookId}_$pageId';
}
