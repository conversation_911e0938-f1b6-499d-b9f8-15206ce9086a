import 'dart:convert';
import 'dart:io';

import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:path/path.dart' as path;

/// Service for caching extracted content to improve performance
class ContentExtractionCacheService {
  static const String _cacheDirectoryName = 'content_extraction_cache';
  static const String _metadataFileName = 'cache_metadata.json';
  static const int _defaultCacheDurationHours = 24;
  static const int _maxCacheSize = 100 * 1024 * 1024; // 100MB

  late final Directory _cacheDirectory;
  late final File _metadataFile;
  Map<String, dynamic> _metadata = {};
  bool _isInitialized = false;

  /// Initialize the cache service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final basePath = getBasePath('');
      _cacheDirectory = Directory(path.join(basePath, _cacheDirectoryName));
      _metadataFile = File(path.join(_cacheDirectory.path, _metadataFileName));

      // Create cache directory if it doesn't exist
      if (!await _cacheDirectory.exists()) {
        await _cacheDirectory.create(recursive: true);
        AnxLog.info('Created content extraction cache directory');
      }

      // Load metadata
      await _loadMetadata();

      // Clean up expired cache entries
      await _cleanupExpiredEntries();

      _isInitialized = true;
      AnxLog.info('ContentExtractionCacheService initialized');
    } catch (e) {
      AnxLog.severe('Failed to initialize ContentExtractionCacheService: $e');
      rethrow;
    }
  }

  /// Cache extraction result for a book
  Future<void> cacheExtraction(
    int bookId,
    ExtractionResult result,
    ExtractionConfig config,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = _generateCacheKey(bookId, config);
      final cacheFile = File(path.join(_cacheDirectory.path, '$cacheKey.json'));

      // Serialize and save the result
      final jsonData = result.toJson();
      await cacheFile.writeAsString(json.encode(jsonData));

      // Update metadata
      _metadata[cacheKey] = {
        'bookId': bookId,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'size': await cacheFile.length(),
        'config': config.toJson(),
      };

      await _saveMetadata();
      AnxLog.info('Cached extraction result for book $bookId');
    } catch (e) {
      AnxLog.warning('Failed to cache extraction result: $e');
    }
  }

  /// Get cached extraction result for a book
  Future<ExtractionResult?> getCachedExtraction(
    int bookId,
    ExtractionConfig config,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = _generateCacheKey(bookId, config);
      final cacheFile = File(path.join(_cacheDirectory.path, '$cacheKey.json'));

      if (!await cacheFile.exists()) return null;

      // Check if cache entry is expired
      final metadata = _metadata[cacheKey];
      if (metadata == null || _isCacheExpired(metadata, config.cacheDurationHours)) {
        await _removeCacheEntry(cacheKey);
        return null;
      }

      // Load and deserialize the result
      final jsonString = await cacheFile.readAsString();
      final jsonData = json.decode(jsonString) as Map<String, dynamic>;
      
      AnxLog.info('Retrieved cached extraction result for book $bookId');
      return ExtractionResult.fromJson(jsonData);
    } catch (e) {
      AnxLog.warning('Failed to get cached extraction result: $e');
      return null;
    }
  }

  /// Cache individual page content
  Future<void> cachePageContent(int bookId, PageContent pageContent) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = _generatePageCacheKey(bookId, pageContent.pageId);
      final cacheFile = File(path.join(_cacheDirectory.path, '$cacheKey.json'));

      // Serialize and save the page content
      final jsonData = pageContent.toJson();
      await cacheFile.writeAsString(json.encode(jsonData));

      // Update metadata
      _metadata[cacheKey] = {
        'bookId': bookId,
        'pageId': pageContent.pageId,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'size': await cacheFile.length(),
        'type': 'page',
      };

      await _saveMetadata();
      AnxLog.info('Cached page content: ${pageContent.pageId} for book $bookId');
    } catch (e) {
      AnxLog.warning('Failed to cache page content: $e');
    }
  }

  /// Get cached page content
  Future<PageContent?> getCachedPageContent(int bookId, String pageId) async {
    if (!_isInitialized) await initialize();

    try {
      final cacheKey = _generatePageCacheKey(bookId, pageId);
      final cacheFile = File(path.join(_cacheDirectory.path, '$cacheKey.json'));

      if (!await cacheFile.exists()) return null;

      // Check if cache entry is expired
      final metadata = _metadata[cacheKey];
      if (metadata == null || _isCacheExpired(metadata, _defaultCacheDurationHours)) {
        await _removeCacheEntry(cacheKey);
        return null;
      }

      // Load and deserialize the page content
      final jsonString = await cacheFile.readAsString();
      final jsonData = json.decode(jsonString) as Map<String, dynamic>;
      
      AnxLog.info('Retrieved cached page content: $pageId for book $bookId');
      return PageContent.fromJson(jsonData);
    } catch (e) {
      AnxLog.warning('Failed to get cached page content: $e');
      return null;
    }
  }

  /// Clear cache for a specific book
  Future<void> clearBookCache(int bookId) async {
    if (!_isInitialized) await initialize();

    try {
      final keysToRemove = <String>[];
      
      for (final entry in _metadata.entries) {
        final metadata = entry.value as Map<String, dynamic>;
        if (metadata['bookId'] == bookId) {
          keysToRemove.add(entry.key);
        }
      }

      for (final key in keysToRemove) {
        await _removeCacheEntry(key);
      }

      AnxLog.info('Cleared cache for book $bookId');
    } catch (e) {
      AnxLog.warning('Failed to clear book cache: $e');
    }
  }

  /// Clear all cached content
  Future<void> clearAllCache() async {
    if (!_isInitialized) await initialize();

    try {
      // Delete all cache files
      await for (final entity in _cacheDirectory.list()) {
        if (entity is File && entity.path != _metadataFile.path) {
          await entity.delete();
        }
      }

      // Clear metadata
      _metadata.clear();
      await _saveMetadata();

      AnxLog.info('Cleared all content extraction cache');
    } catch (e) {
      AnxLog.warning('Failed to clear all cache: $e');
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getStatistics() async {
    if (!_isInitialized) await initialize();

    try {
      int totalSize = 0;
      int totalEntries = 0;
      int expiredEntries = 0;
      final bookCounts = <int, int>{};

      for (final entry in _metadata.entries) {
        final metadata = entry.value as Map<String, dynamic>;
        totalEntries++;
        totalSize += (metadata['size'] as int?) ?? 0;
        
        final bookId = metadata['bookId'] as int?;
        if (bookId != null) {
          bookCounts[bookId] = (bookCounts[bookId] ?? 0) + 1;
        }

        if (_isCacheExpired(metadata, _defaultCacheDurationHours)) {
          expiredEntries++;
        }
      }

      return {
        'totalEntries': totalEntries,
        'totalSize': totalSize,
        'totalSizeMB': (totalSize / (1024 * 1024)).toStringAsFixed(2),
        'expiredEntries': expiredEntries,
        'booksWithCache': bookCounts.length,
        'averageEntriesPerBook': bookCounts.isNotEmpty 
            ? (totalEntries / bookCounts.length).toStringAsFixed(1)
            : '0',
      };
    } catch (e) {
      AnxLog.warning('Failed to get cache statistics: $e');
      return {'error': e.toString()};
    }
  }

  /// Generate cache key for extraction result
  String _generateCacheKey(int bookId, ExtractionConfig config) {
    final configHash = config.hashCode;
    return 'extraction_${bookId}_$configHash';
  }

  /// Generate cache key for page content
  String _generatePageCacheKey(int bookId, String pageId) {
    final pageIdHash = pageId.hashCode;
    return 'page_${bookId}_$pageIdHash';
  }

  /// Check if cache entry is expired
  bool _isCacheExpired(Map<String, dynamic> metadata, int cacheDurationHours) {
    final timestamp = metadata['timestamp'] as int?;
    if (timestamp == null) return true;

    final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final expiryTime = cacheTime.add(Duration(hours: cacheDurationHours));
    
    return DateTime.now().isAfter(expiryTime);
  }

  /// Remove a cache entry
  Future<void> _removeCacheEntry(String cacheKey) async {
    try {
      final cacheFile = File(path.join(_cacheDirectory.path, '$cacheKey.json'));
      if (await cacheFile.exists()) {
        await cacheFile.delete();
      }
      
      _metadata.remove(cacheKey);
      await _saveMetadata();
    } catch (e) {
      AnxLog.warning('Failed to remove cache entry $cacheKey: $e');
    }
  }

  /// Load metadata from file
  Future<void> _loadMetadata() async {
    try {
      if (await _metadataFile.exists()) {
        final jsonString = await _metadataFile.readAsString();
        _metadata = json.decode(jsonString) as Map<String, dynamic>;
      } else {
        _metadata = {};
      }
    } catch (e) {
      AnxLog.warning('Failed to load cache metadata: $e');
      _metadata = {};
    }
  }

  /// Save metadata to file
  Future<void> _saveMetadata() async {
    try {
      await _metadataFile.writeAsString(json.encode(_metadata));
    } catch (e) {
      AnxLog.warning('Failed to save cache metadata: $e');
    }
  }

  /// Clean up expired cache entries
  Future<void> _cleanupExpiredEntries() async {
    try {
      final keysToRemove = <String>[];
      
      for (final entry in _metadata.entries) {
        final metadata = entry.value as Map<String, dynamic>;
        if (_isCacheExpired(metadata, _defaultCacheDurationHours)) {
          keysToRemove.add(entry.key);
        }
      }

      for (final key in keysToRemove) {
        await _removeCacheEntry(key);
      }

      if (keysToRemove.isNotEmpty) {
        AnxLog.info('Cleaned up ${keysToRemove.length} expired cache entries');
      }
    } catch (e) {
      AnxLog.warning('Failed to cleanup expired entries: $e');
    }
  }

  /// Dispose of resources
  Future<void> dispose() async {
    // Nothing to dispose for now
    AnxLog.info('ContentExtractionCacheService disposed');
  }
}
