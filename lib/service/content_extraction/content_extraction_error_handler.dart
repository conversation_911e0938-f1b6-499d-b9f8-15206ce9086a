import 'dart:async';
import 'dart:io';

import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Custom exceptions for content extraction
class ContentExtractionException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  final StackTrace? stackTrace;

  const ContentExtractionException(
    this.message, {
    this.code,
    this.originalError,
    this.stackTrace,
  });

  @override
  String toString() {
    return 'ContentExtractionException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}

/// Error codes for different types of extraction errors
class ContentExtractionErrorCodes {
  static const String fileNotFound = 'FILE_NOT_FOUND';
  static const String unsupportedFormat = 'UNSUPPORTED_FORMAT';
  static const String corruptedFile = 'CORRUPTED_FILE';
  static const String webViewError = 'WEBVIEW_ERROR';
  static const String networkError = 'NETWORK_ERROR';
  static const String memoryError = 'MEMORY_ERROR';
  static const String timeoutError = 'TIMEOUT_ERROR';
  static const String permissionError = 'PERMISSION_ERROR';
  static const String cacheError = 'CACHE_ERROR';
  static const String segmentationError = 'SEGMENTATION_ERROR';
  static const String processingError = 'PROCESSING_ERROR';
}

/// Error handler and logger for content extraction operations
class ContentExtractionErrorHandler {
  static final ContentExtractionErrorHandler _instance = 
      ContentExtractionErrorHandler._internal();
  
  factory ContentExtractionErrorHandler() => _instance;
  
  ContentExtractionErrorHandler._internal();

  /// Handle and log extraction errors
  ContentExtractionException handleError(
    dynamic error,
    StackTrace? stackTrace, {
    String? context,
    Book? book,
    String? operation,
  }) {
    final contextInfo = _buildContextInfo(context, book, operation);
    
    // Determine error type and create appropriate exception
    final extractionError = _categorizeError(error, stackTrace);
    
    // Log the error with appropriate level
    _logError(extractionError, contextInfo);
    
    return extractionError;
  }

  /// Handle timeout errors specifically
  ContentExtractionException handleTimeout(
    String operation, {
    Duration? timeout,
    Book? book,
  }) {
    final message = 'Operation timed out: $operation'
        '${timeout != null ? ' (timeout: ${timeout.inSeconds}s)' : ''}';
    
    final error = ContentExtractionException(
      message,
      code: ContentExtractionErrorCodes.timeoutError,
    );

    final contextInfo = _buildContextInfo('Timeout', book, operation);
    _logError(error, contextInfo);
    
    return error;
  }

  /// Handle file-related errors
  ContentExtractionException handleFileError(
    dynamic error,
    String filePath, {
    Book? book,
  }) {
    String message;
    String code;

    if (error is FileSystemException) {
      switch (error.osError?.errorCode) {
        case 2: // File not found
          message = 'File not found: $filePath';
          code = ContentExtractionErrorCodes.fileNotFound;
          break;
        case 13: // Permission denied
          message = 'Permission denied accessing file: $filePath';
          code = ContentExtractionErrorCodes.permissionError;
          break;
        default:
          message = 'File system error: ${error.message}';
          code = ContentExtractionErrorCodes.processingError;
      }
    } else {
      message = 'File error: $error';
      code = ContentExtractionErrorCodes.processingError;
    }

    final extractionError = ContentExtractionException(
      message,
      code: code,
      originalError: error,
    );

    final contextInfo = _buildContextInfo('File Operation', book, 'File access');
    _logError(extractionError, contextInfo);
    
    return extractionError;
  }

  /// Handle WebView-related errors
  ContentExtractionException handleWebViewError(
    dynamic error, {
    Book? book,
    String? operation,
  }) {
    final message = 'WebView error during content extraction: $error';
    
    final extractionError = ContentExtractionException(
      message,
      code: ContentExtractionErrorCodes.webViewError,
      originalError: error,
    );

    final contextInfo = _buildContextInfo('WebView', book, operation);
    _logError(extractionError, contextInfo);
    
    return extractionError;
  }

  /// Create fallback extraction result for errors
  ExtractionResult createFallbackResult(
    ContentExtractionException error,
    DateTime startTime, {
    List<String>? warnings,
  }) {
    return ExtractionResult(
      success: false,
      errorMessage: error.message,
      warnings: warnings ?? [],
      startTime: startTime,
      endTime: DateTime.now(),
      statistics: {
        'errorCode': error.code,
        'errorType': 'ContentExtractionException',
      },
    );
  }

  /// Create fallback page content for errors
  PageContent? createFallbackPageContent(
    Book book,
    String pageId,
    ContentExtractionException error,
  ) {
    // For some errors, we might want to return minimal content
    // For others, we return null to indicate complete failure
    switch (error.code) {
      case ContentExtractionErrorCodes.timeoutError:
      case ContentExtractionErrorCodes.networkError:
        // These might be temporary, return minimal content
        return PageContent(
          pageId: pageId,
          bookId: book.id,
          rawText: '[Content extraction failed: ${error.message}]',
          characterCount: 0,
          wordCount: 0,
          format: _determineBookFormat(book.fileFullPath),
          extractedAt: DateTime.now(),
          metadata: {
            'error': error.message,
            'errorCode': error.code,
          },
        );
      default:
        // For other errors, return null to indicate failure
        return null;
    }
  }

  /// Retry extraction with exponential backoff
  Future<T> retryWithBackoff<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    bool Function(dynamic error)? shouldRetry,
  }) async {
    int attempt = 0;
    Duration delay = initialDelay;

    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (error, stackTrace) {
        attempt++;
        
        // Check if we should retry this error
        if (shouldRetry != null && !shouldRetry(error)) {
          rethrow;
        }
        
        if (attempt >= maxRetries) {
          AnxLog.severe('Operation failed after $maxRetries attempts: $error');
          rethrow;
        }

        AnxLog.warning('Operation failed (attempt $attempt/$maxRetries), retrying in ${delay.inSeconds}s: $error');
        
        await Future.delayed(delay);
        delay = Duration(milliseconds: (delay.inMilliseconds * backoffMultiplier).round());
      }
    }

    throw StateError('Retry logic error'); // Should never reach here
  }

  /// Check if an error is retryable
  bool isRetryableError(dynamic error) {
    if (error is ContentExtractionException) {
      switch (error.code) {
        case ContentExtractionErrorCodes.networkError:
        case ContentExtractionErrorCodes.timeoutError:
        case ContentExtractionErrorCodes.webViewError:
          return true;
        case ContentExtractionErrorCodes.fileNotFound:
        case ContentExtractionErrorCodes.unsupportedFormat:
        case ContentExtractionErrorCodes.corruptedFile:
        case ContentExtractionErrorCodes.permissionError:
          return false;
        default:
          return true; // Default to retryable for unknown errors
      }
    }
    
    if (error is TimeoutException || error is SocketException) {
      return true;
    }
    
    return false;
  }

  /// Categorize error and create appropriate exception
  ContentExtractionException _categorizeError(dynamic error, StackTrace? stackTrace) {
    if (error is ContentExtractionException) {
      return error;
    }

    String message;
    String code;

    if (error is TimeoutException) {
      message = 'Operation timed out: ${error.message ?? 'Unknown timeout'}';
      code = ContentExtractionErrorCodes.timeoutError;
    } else if (error is SocketException) {
      message = 'Network error: ${error.message}';
      code = ContentExtractionErrorCodes.networkError;
    } else if (error is FileSystemException) {
      message = 'File system error: ${error.message}';
      code = ContentExtractionErrorCodes.fileNotFound;
    } else if (error is OutOfMemoryError) {
      message = 'Out of memory during content extraction';
      code = ContentExtractionErrorCodes.memoryError;
    } else if (error is FormatException) {
      message = 'Format error: ${error.message}';
      code = ContentExtractionErrorCodes.corruptedFile;
    } else {
      message = 'Unexpected error during content extraction: $error';
      code = ContentExtractionErrorCodes.processingError;
    }

    return ContentExtractionException(
      message,
      code: code,
      originalError: error,
      stackTrace: stackTrace,
    );
  }

  /// Build context information for logging
  String _buildContextInfo(String? context, Book? book, String? operation) {
    final parts = <String>[];
    
    if (context != null) parts.add('Context: $context');
    if (book != null) parts.add('Book: ${book.title} (ID: ${book.id})');
    if (operation != null) parts.add('Operation: $operation');
    
    return parts.join(', ');
  }

  /// Log error with appropriate level
  void _logError(ContentExtractionException error, String contextInfo) {
    final logMessage = '${error.message}${contextInfo.isNotEmpty ? ' | $contextInfo' : ''}';
    
    switch (error.code) {
      case ContentExtractionErrorCodes.fileNotFound:
      case ContentExtractionErrorCodes.permissionError:
        AnxLog.warning(logMessage);
        break;
      case ContentExtractionErrorCodes.timeoutError:
      case ContentExtractionErrorCodes.networkError:
        AnxLog.info(logMessage); // These might be temporary
        break;
      default:
        AnxLog.severe(logMessage);
        if (error.stackTrace != null) {
          AnxLog.severe('Stack trace: ${error.stackTrace}');
        }
    }
  }

  /// Determine book format from file path
  BookFormat _determineBookFormat(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    
    switch (extension) {
      case 'epub':
        return BookFormat.epub;
      case 'pdf':
        return BookFormat.pdf;
      case 'txt':
        return BookFormat.txt;
      case 'mobi':
      case 'azw':
      case 'azw3':
        return BookFormat.mobi;
      case 'fb2':
        return BookFormat.fb2;
      case 'cbz':
      case 'cbr':
        return BookFormat.comic;
      default:
        return BookFormat.epub;
    }
  }
}
