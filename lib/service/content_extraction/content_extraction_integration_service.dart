import 'dart:async';

import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/service/content_extraction/page_content_extraction_service.dart';
import 'package:dasso_reader/service/dictionary/chinese_segmentation_service.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/service/ai/index.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Service that integrates content extraction with other app services
class ContentExtractionIntegrationService {
  static final ContentExtractionIntegrationService _instance = 
      ContentExtractionIntegrationService._internal();
  
  factory ContentExtractionIntegrationService() => _instance;
  
  ContentExtractionIntegrationService._internal();

  late final PageContentExtractionService _extractionService;
  late final ChineseSegmentationService _segmentationService;
  late final DictionaryService _dictionaryService;
  
  bool _isInitialized = false;

  /// Initialize the integration service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AnxLog.info('Initializing ContentExtractionIntegrationService...');
      
      _extractionService = PageContentExtractionService();
      await _extractionService.initialize();
      
      _segmentationService = ChineseSegmentationService();
      await _segmentationService.initialize();
      
      _dictionaryService = DictionaryService();
      
      _isInitialized = true;
      AnxLog.info('ContentExtractionIntegrationService initialized');
    } catch (e) {
      AnxLog.severe('Failed to initialize ContentExtractionIntegrationService: $e');
      rethrow;
    }
  }

  /// Extract content and preprocess Chinese text for segmentation
  Future<ExtractionResult> extractAndPreprocessContent(
    Book book, {
    ExtractionConfig? config,
    bool preprocessChinese = true,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      AnxLog.info('Extracting and preprocessing content for: ${book.title}');
      
      // Extract content first
      final result = await _extractionService.extractContent(
        book,
        config: config,
      );

      if (!result.success || !preprocessChinese) {
        return result;
      }

      // Preprocess Chinese content in background
      final chinesePages = result.pages.where((page) => page.containsChinese).toList();
      
      if (chinesePages.isNotEmpty) {
        AnxLog.info('Preprocessing ${chinesePages.length} pages with Chinese content');
        
        // Process in background to avoid blocking
        unawaited(_preprocessChineseContent(book.id, chinesePages));
      }

      return result;
    } catch (e) {
      AnxLog.severe('Failed to extract and preprocess content: $e');
      rethrow;
    }
  }

  /// Extract content and analyze vocabulary for learning
  Future<Map<String, dynamic>> extractAndAnalyzeVocabulary(
    Book book, {
    ExtractionConfig? config,
    int? hskLevelFilter,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      AnxLog.info('Extracting and analyzing vocabulary for: ${book.title}');
      
      // Extract content
      final result = await _extractionService.extractContent(
        book,
        config: config,
      );

      if (!result.success) {
        return {
          'success': false,
          'error': result.errorMessage,
        };
      }

      // Analyze Chinese vocabulary
      final vocabularyAnalysis = await _analyzeChineseVocabulary(
        result.pages,
        hskLevelFilter: hskLevelFilter,
      );

      return {
        'success': true,
        'extractionResult': result,
        'vocabularyAnalysis': vocabularyAnalysis,
      };
    } catch (e) {
      AnxLog.severe('Failed to extract and analyze vocabulary: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Extract content and prepare for AI analysis
  Future<Map<String, dynamic>> extractForAIAnalysis(
    Book book, {
    ExtractionConfig? config,
    int maxContentLength = 10000,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      AnxLog.info('Extracting content for AI analysis: ${book.title}');
      
      // Extract content with specific config for AI
      final aiConfig = config?.copyWith(
        segmentContent: true,
        maxSegmentLength: 1000,
        includeHtml: false,
      ) ?? const ExtractionConfig(
        segmentContent: true,
        maxSegmentLength: 1000,
        includeHtml: false,
      );

      final result = await _extractionService.extractContent(
        book,
        config: aiConfig,
      );

      if (!result.success) {
        return {
          'success': false,
          'error': result.errorMessage,
        };
      }

      // Prepare content for AI analysis
      final aiReadyContent = _prepareContentForAI(
        result.pages,
        maxLength: maxContentLength,
      );

      return {
        'success': true,
        'extractionResult': result,
        'aiContent': aiReadyContent,
      };
    } catch (e) {
      AnxLog.severe('Failed to extract content for AI analysis: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Get dictionary entries for extracted content
  Future<List<DictionaryEntry>> getDictionaryEntriesForContent(
    PageContent pageContent, {
    int maxEntries = 50,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      if (!pageContent.containsChinese) {
        return [];
      }

      // Segment the text to get individual words
      final boundaries = await _segmentationService.getWordBoundaries(
        pageContent.rawText,
        bookId: pageContent.bookId,
      );

      final words = <String>[];
      for (final boundary in boundaries) {
        final word = pageContent.rawText.substring(boundary[0], boundary[1]);
        if (word.trim().isNotEmpty && word.length > 1) {
          words.add(word);
        }
      }

      // Get dictionary entries for unique words
      final uniqueWords = words.toSet().take(maxEntries).toList();
      final entries = <DictionaryEntry>[];

      for (final word in uniqueWords) {
        try {
          final entry = await _dictionaryService.lookupWord(word);
          if (entry != null) {
            entries.add(entry);
          }
        } catch (e) {
          AnxLog.warning('Failed to lookup word: $word: $e');
        }
      }

      return entries;
    } catch (e) {
      AnxLog.severe('Failed to get dictionary entries: $e');
      return [];
    }
  }

  /// Preprocess Chinese content for better segmentation
  Future<void> _preprocessChineseContent(
    int bookId,
    List<PageContent> chinesePages,
  ) async {
    try {
      for (final page in chinesePages) {
        // Process page content in chunks
        final chunks = _splitIntoChunks(page.rawText, 500);
        
        for (final chunk in chunks) {
          if (chunk.trim().isEmpty) continue;
          
          try {
            // This will cache the segmentation results
            await _segmentationService.getWordBoundaries(
              chunk,
              bookId: bookId,
            );
          } catch (e) {
            AnxLog.warning('Error preprocessing chunk: $e');
          }
        }
      }
      
      AnxLog.info('Completed preprocessing Chinese content for book: $bookId');
    } catch (e) {
      AnxLog.warning('Error preprocessing Chinese content: $e');
    }
  }

  /// Analyze Chinese vocabulary in extracted content
  Future<Map<String, dynamic>> _analyzeChineseVocabulary(
    List<PageContent> pages, {
    int? hskLevelFilter,
  }) async {
    try {
      final vocabularyStats = <String, dynamic>{
        'totalWords': 0,
        'uniqueWords': 0,
        'chineseWords': 0,
        'hskWords': <String, int>{},
        'wordFrequency': <String, int>{},
      };

      final allWords = <String>[];
      final wordFrequency = <String, int>{};

      // Extract words from all Chinese pages
      for (final page in pages.where((p) => p.containsChinese)) {
        final boundaries = await _segmentationService.getWordBoundaries(
          page.rawText,
          bookId: page.bookId,
        );

        for (final boundary in boundaries) {
          final word = page.rawText.substring(boundary[0], boundary[1]);
          if (word.trim().isNotEmpty && word.length > 1) {
            allWords.add(word);
            wordFrequency[word] = (wordFrequency[word] ?? 0) + 1;
          }
        }
      }

      vocabularyStats['totalWords'] = allWords.length;
      vocabularyStats['uniqueWords'] = wordFrequency.keys.length;
      vocabularyStats['chineseWords'] = allWords.where((w) => 
          RegExp(r'[\u4e00-\u9fa5]').hasMatch(w)).length;
      vocabularyStats['wordFrequency'] = wordFrequency;

      // Analyze HSK levels if dictionary service is available
      final hskStats = <String, int>{};
      final topWords = wordFrequency.entries
          .toList()
          ..sort((a, b) => b.value.compareTo(a.value));

      for (final entry in topWords.take(100)) {
        try {
          final dictEntry = await _dictionaryService.lookupWord(entry.key);
          if (dictEntry?.hskLevel != null) {
            final level = 'HSK${dictEntry!.hskLevel}';
            hskStats[level] = (hskStats[level] ?? 0) + 1;
          }
        } catch (e) {
          // Ignore lookup errors
        }
      }

      vocabularyStats['hskWords'] = hskStats;

      return vocabularyStats;
    } catch (e) {
      AnxLog.warning('Error analyzing vocabulary: $e');
      return {'error': e.toString()};
    }
  }

  /// Prepare content for AI analysis
  Map<String, dynamic> _prepareContentForAI(
    List<PageContent> pages, {
    int maxLength = 10000,
  }) {
    try {
      final allText = pages.map((p) => p.rawText).join('\n\n');
      
      // Truncate if too long
      final truncatedText = allText.length > maxLength
          ? allText.substring(0, maxLength) + '...'
          : allText;

      return {
        'text': truncatedText,
        'totalPages': pages.length,
        'totalCharacters': allText.length,
        'containsChinese': pages.any((p) => p.containsChinese),
        'languages': pages.expand((p) => p.languages).toSet().toList(),
        'segments': pages.expand((p) => p.segments).take(20).toList(),
      };
    } catch (e) {
      AnxLog.warning('Error preparing content for AI: $e');
      return {'error': e.toString()};
    }
  }

  /// Split text into chunks
  List<String> _splitIntoChunks(String text, int chunkSize) {
    final chunks = <String>[];
    for (int i = 0; i < text.length; i += chunkSize) {
      final end = (i + chunkSize < text.length) ? i + chunkSize : text.length;
      chunks.add(text.substring(i, end));
    }
    return chunks;
  }

  /// Dispose of resources
  Future<void> dispose() async {
    if (!_isInitialized) return;
    
    try {
      await _extractionService.dispose();
      _isInitialized = false;
      AnxLog.info('ContentExtractionIntegrationService disposed');
    } catch (e) {
      AnxLog.warning('Error disposing ContentExtractionIntegrationService: $e');
    }
  }
}
