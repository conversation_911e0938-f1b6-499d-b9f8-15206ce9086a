import 'dart:async';
import 'dart:convert';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/service/book_player/book_player_server.dart';
import 'package:dasso_reader/service/content_extraction/content_processing_utils.dart';
import 'package:dasso_reader/service/cache/content_extraction_cache_service.dart';
import 'package:dasso_reader/service/content_extraction/base_content_extractor.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Extractor for PDF content using PDF.js integration
class PdfContentExtractor implements BaseContentExtractor {
  final ContentProcessingUtils _processingUtils;
  final ContentExtractionCacheService _cacheService;

  HeadlessInAppWebView? _headlessWebView;
  InAppWebViewController? _webViewController;
  bool _isInitialized = false;
  bool _isExtracting = false;

  final StreamController<ExtractionProgress> _progressController =
      StreamController<ExtractionProgress>.broadcast();

  PdfContentExtractor(this._processingUtils, this._cacheService);

  /// Initialize the PDF extractor
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AnxLog.info('Initializing PdfContentExtractor...');
      _isInitialized = true;
      AnxLog.info('PdfContentExtractor initialized');
    } catch (e) {
      AnxLog.severe('Failed to initialize PdfContentExtractor: $e');
      rethrow;
    }
  }

  /// Extract content from a PDF book
  @override
  Future<ExtractionResult> extractContent(
    Book book,
    ExtractionConfig config,
    Stream<ExtractionProgress>? progressStream,
    DateTime startTime,
  ) async {
    if (!_isInitialized) await initialize();
    if (_isExtracting) {
      throw StateError('Extraction already in progress');
    }

    _isExtracting = true;
    final pages = <PageContent>[];
    final warnings = <String>[];

    try {
      AnxLog.info('Starting PDF content extraction for: ${book.title}');

      // Initialize headless WebView for PDF processing
      await _initializeWebView(book);

      // Get PDF information
      final pdfInfo = await _getPdfInfo();
      final totalPages = pdfInfo['numPages'] as int? ?? 0;

      if (totalPages == 0) {
        warnings.add('No pages found in PDF');
        return ExtractionResult(
          success: false,
          errorMessage: 'No pages found in PDF',
          warnings: warnings,
          startTime: startTime,
          endTime: DateTime.now(),
        );
      }

      // Extract content from each page
      for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
        if (!_isExtracting) break; // Check for cancellation

        final progress = ExtractionProgress(
          currentPage: pageNum,
          totalPages: totalPages,
          operation: 'Extracting page $pageNum of $totalPages',
          percentage: (pageNum / totalPages) * 100,
        );

        _progressController.add(progress);

        try {
          final pageContent = await _extractPageContent(
            book,
            pageNum,
            config,
          );

          if (pageContent != null) {
            pages.add(pageContent);
          }
        } catch (e) {
          warnings.add('Failed to extract page $pageNum: $e');
          AnxLog.warning('Failed to extract page $pageNum: $e');
        }
      }

      final endTime = DateTime.now();
      final processingTime = endTime.difference(startTime).inMilliseconds;

      // Calculate statistics
      final statistics = _processingUtils.calculateStatistics(pages);

      final result = ExtractionResult(
        success: pages.isNotEmpty,
        pages: pages,
        totalPages: pages.length,
        totalProcessingTimeMs: processingTime,
        warnings: warnings,
        statistics: statistics,
        startTime: startTime,
        endTime: endTime,
      );

      AnxLog.info(
        'PDF extraction completed: ${pages.length} pages extracted '
        'in ${processingTime}ms',
      );

      return result;
    } catch (e) {
      AnxLog.severe('PDF content extraction failed: $e');
      return ExtractionResult(
        success: false,
        errorMessage: e.toString(),
        warnings: warnings,
        startTime: startTime,
        endTime: DateTime.now(),
      );
    } finally {
      _isExtracting = false;
      await _disposeWebView();
    }
  }

  /// Extract content from a specific page
  @override
  Future<PageContent?> extractPageContent(
    Book book,
    String pageId,
    ExtractionConfig config,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      final pageNumber = int.tryParse(pageId);
      if (pageNumber == null) {
        AnxLog.warning('Invalid page ID for PDF: $pageId');
        return null;
      }

      AnxLog.info('Extracting PDF page content: $pageId');

      await _initializeWebView(book);

      // Extract content from specific page
      final content = await _extractPageContent(book, pageNumber, config);

      return content;
    } catch (e) {
      AnxLog.severe('Failed to extract PDF page content: $e');
      return null;
    } finally {
      await _disposeWebView();
    }
  }

  /// Get progress stream
  @override
  Stream<ExtractionProgress> getProgressStream() {
    return _progressController.stream;
  }

  /// Cancel ongoing extraction
  @override
  Future<void> cancelExtraction() async {
    _isExtracting = false;
    await _disposeWebView();
    AnxLog.info('PDF extraction cancelled');
  }

  /// Initialize headless WebView for PDF processing
  Future<void> _initializeWebView(Book book) async {
    try {
      final serverUrl = 'http://localhost:${Server().port}';
      final bookUrl =
          '$serverUrl/book/${Uri.encodeComponent(book.fileFullPath)}';
      final indexUrl = '$serverUrl/foliate-js/index.html';

      _headlessWebView = HeadlessInAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(indexUrl)),
        onWebViewCreated: (controller) {
          _webViewController = controller;
        },
        onLoadStop: (controller, url) async {
          // Initialize the PDF in the WebView
          await controller.evaluateJavascript(source: '''
            (async function() {
              try {
                if (typeof getView === 'function') {
                  const response = await fetch('$bookUrl');
                  const blob = await response.blob();
                  const file = new File([blob], '${book.title}.pdf', { type: 'application/pdf' });
                  window.bookView = await getView(file);
                  return 'PDF loaded successfully';
                } else {
                  throw new Error('getView function not available');
                }
              } catch (e) {
                console.error('Error loading PDF:', e);
                throw e;
              }
            })();
          ''');
        },
      );

      await _headlessWebView!.run();

      // Wait for PDF to load
      await Future<void>.delayed(const Duration(seconds: 3));

      AnxLog.info('WebView initialized for PDF extraction');
    } catch (e) {
      AnxLog.severe('Failed to initialize WebView for PDF: $e');
      rethrow;
    }
  }

  /// Get PDF information
  Future<Map<String, dynamic>> _getPdfInfo() async {
    if (_webViewController == null) return {};

    try {
      final result = await _webViewController!.evaluateJavascript(source: '''
        (function() {
          try {
            if (!window.bookView || !window.bookView.book) {
              return {};
            }
            
            const book = window.bookView.book;
            return {
              numPages: book.sections ? book.sections.length : 0,
              title: book.metadata ? book.metadata.title : null,
              author: book.metadata ? book.metadata.author : null,
            };
          } catch (e) {
            console.error('Error getting PDF info:', e);
            return {};
          }
        })();
      ''');

      if (result is Map) {
        return Map<String, dynamic>.from(result);
      }

      return {};
    } catch (e) {
      AnxLog.warning('Failed to get PDF info: $e');
      return {};
    }
  }

  /// Extract content from a specific PDF page
  Future<PageContent?> _extractPageContent(
    Book book,
    int pageNumber,
    ExtractionConfig config,
  ) async {
    if (_webViewController == null) return null;

    try {
      final extractionStart = DateTime.now();

      // Navigate to specific page
      await _webViewController!.evaluateJavascript(source: '''
        (async function() {
          try {
            if (window.bookView && window.bookView.goTo) {
              await window.bookView.goTo(${pageNumber - 1}); // 0-based index
              // Wait for page to load
              await new Promise(resolve => setTimeout(resolve, 1500));
            }
          } catch (e) {
            console.error('Error navigating to page:', e);
          }
        })();
      ''');

      // Extract text content from PDF page
      final result = await _webViewController!.evaluateJavascript(source: '''
        (function() {
          try {
            const content = {
              text: '',
              elements: []
            };
            
            // Look for text layer elements (PDF.js creates these)
            const textLayerElements = document.querySelectorAll('.textLayer span, .textLayer div');
            if (textLayerElements.length > 0) {
              // Extract text from text layer
              content.text = Array.from(textLayerElements)
                .map(el => el.textContent)
                .filter(text => text && text.trim().length > 0)
                .join(' ');
              
              // Extract element information
              textLayerElements.forEach((el, index) => {
                const text = el.textContent;
                if (text && text.trim().length > 0) {
                  content.elements.push({
                    text: text,
                    index: index,
                    tagName: el.tagName.toLowerCase(),
                    className: el.className
                  });
                }
              });
            } else {
              // Fallback: try to get any visible text
              const allElements = document.querySelectorAll('*');
              const textElements = Array.from(allElements).filter(el => {
                const text = el.textContent;
                return text && text.trim().length > 0 && 
                       !el.querySelector('*'); // Only leaf elements
              });
              
              content.text = textElements
                .map(el => el.textContent)
                .join(' ');
            }
            
            return content;
          } catch (e) {
            console.error('Error extracting PDF content:', e);
            return { text: '', elements: [] };
          }
        })();
      ''');

      if (result is! Map) return null;

      final contentMap = Map<String, dynamic>.from(result);
      final rawText = contentMap['text']?.toString() ?? '';

      if (rawText.isEmpty) return null;

      // Clean and process text
      final cleanedText = _processingUtils.cleanText(rawText);
      final containsChinese = _processingUtils.containsChinese(cleanedText);
      final languages = _processingUtils.detectLanguages(cleanedText);
      final wordCount = _processingUtils.countWords(cleanedText);
      final paragraphCount = _processingUtils.countParagraphs(cleanedText);

      // Segment content if requested
      final segments = config.segmentContent
          ? _processingUtils.segmentContent(
              cleanedText,
              maxLength: config.maxSegmentLength,
            )
          : <ContentSegment>[];

      final processingTime =
          DateTime.now().difference(extractionStart).inMilliseconds;

      return PageContent(
        pageId: pageNumber.toString(),
        bookId: book.id,
        pageNumber: pageNumber,
        rawText: cleanedText,
        segments: segments,
        characterCount: cleanedText.length,
        wordCount: wordCount,
        paragraphCount: paragraphCount,
        containsChinese: containsChinese,
        languages: languages,
        format: BookFormat.pdf,
        extractedAt: DateTime.now(),
        processingTimeMs: processingTime,
      );
    } catch (e) {
      AnxLog.warning('Failed to extract PDF page $pageNumber content: $e');
      return null;
    }
  }

  /// Dispose of WebView resources
  Future<void> _disposeWebView() async {
    try {
      await _headlessWebView?.dispose();
      _headlessWebView = null;
      _webViewController = null;
    } catch (e) {
      AnxLog.warning('Error disposing PDF WebView: $e');
    }
  }

  /// Dispose of resources
  @override
  Future<void> dispose() async {
    _isExtracting = false;
    await _disposeWebView();
    await _progressController.close();
    AnxLog.info('PdfContentExtractor disposed');
  }
}
