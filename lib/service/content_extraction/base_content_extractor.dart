import 'dart:async';

import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/page_content.dart';

/// Base interface for content extractors
abstract class BaseContentExtractor {
  /// Initialize the extractor
  Future<void> initialize();

  /// Extract content from a book
  Future<ExtractionResult> extractContent(
    Book book,
    ExtractionConfig config,
    Stream<ExtractionProgress>? progressStream,
    DateTime startTime,
  );

  /// Extract content from a specific page
  Future<PageContent?> extractPageContent(
    Book book,
    String pageId,
    ExtractionConfig config,
  );

  /// Get progress stream
  Stream<ExtractionProgress> getProgressStream();

  /// Cancel ongoing extraction
  Future<void> cancelExtraction();

  /// Dispose of resources
  Future<void> dispose();
}
