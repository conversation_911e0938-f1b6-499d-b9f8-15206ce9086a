import 'dart:async';
import 'dart:convert';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/service/book_player/book_player_server.dart';
import 'package:dasso_reader/service/content_extraction/content_processing_utils.dart';
import 'package:dasso_reader/service/cache/content_extraction_cache_service.dart';
import 'package:dasso_reader/service/content_extraction/base_content_extractor.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Extractor for EPUB content using WebView JavaScript evaluation
class EpubContentExtractor implements BaseContentExtractor {
  final ContentProcessingUtils _processingUtils;
  final ContentExtractionCacheService _cacheService;

  HeadlessInAppWebView? _headlessWebView;
  InAppWebViewController? _webViewController;
  bool _isInitialized = false;
  bool _isExtracting = false;

  final StreamController<ExtractionProgress> _progressController =
      StreamController<ExtractionProgress>.broadcast();

  EpubContentExtractor(this._processingUtils, this._cacheService);

  /// Initialize the EPUB extractor
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AnxLog.info('Initializing EpubContentExtractor...');
      _isInitialized = true;
      AnxLog.info('EpubContentExtractor initialized');
    } catch (e) {
      AnxLog.severe('Failed to initialize EpubContentExtractor: $e');
      rethrow;
    }
  }

  /// Extract content from an EPUB book
  @override
  Future<ExtractionResult> extractContent(
    Book book,
    ExtractionConfig config,
    Stream<ExtractionProgress>? progressStream,
    DateTime startTime,
  ) async {
    if (!_isInitialized) await initialize();
    if (_isExtracting) {
      throw StateError('Extraction already in progress');
    }

    _isExtracting = true;
    final pages = <PageContent>[];
    final warnings = <String>[];

    try {
      AnxLog.info('Starting EPUB content extraction for: ${book.title}');

      // Initialize headless WebView for content extraction
      await _initializeWebView(book);

      // Get book structure (chapters/sections)
      final bookStructure = await _getBookStructure();
      final totalSections = bookStructure.length;

      if (totalSections == 0) {
        warnings.add('No sections found in EPUB');
        return ExtractionResult(
          success: false,
          errorMessage: 'No content sections found',
          warnings: warnings,
          startTime: startTime,
          endTime: DateTime.now(),
        );
      }

      // Extract content from each section
      for (int i = 0; i < bookStructure.length; i++) {
        if (!_isExtracting) break; // Check for cancellation

        final section = bookStructure[i];
        final progress = ExtractionProgress(
          currentPage: i + 1,
          totalPages: totalSections,
          operation: 'Extracting: ${section['title'] ?? 'Section ${i + 1}'}',
          percentage: ((i + 1) / totalSections) * 100,
        );

        _progressController.add(progress);

        try {
          final pageContent = await _extractSectionContent(
            book,
            section,
            i,
            config,
          );

          if (pageContent != null) {
            pages.add(pageContent);
          }
        } catch (e) {
          warnings.add('Failed to extract section ${i + 1}: $e');
          AnxLog.warning('Failed to extract section ${i + 1}: $e');
        }
      }

      final endTime = DateTime.now();
      final processingTime = endTime.difference(startTime).inMilliseconds;

      // Calculate statistics
      final statistics = _processingUtils.calculateStatistics(pages);

      final result = ExtractionResult(
        success: pages.isNotEmpty,
        pages: pages,
        totalPages: pages.length,
        totalProcessingTimeMs: processingTime,
        warnings: warnings,
        statistics: statistics,
        startTime: startTime,
        endTime: endTime,
      );

      AnxLog.info(
        'EPUB extraction completed: ${pages.length} pages extracted '
        'in ${processingTime}ms',
      );

      return result;
    } catch (e) {
      AnxLog.severe('EPUB content extraction failed: $e');
      return ExtractionResult(
        success: false,
        errorMessage: e.toString(),
        warnings: warnings,
        startTime: startTime,
        endTime: DateTime.now(),
      );
    } finally {
      _isExtracting = false;
      await _disposeWebView();
    }
  }

  /// Extract content from a specific page/section
  @override
  Future<PageContent?> extractPageContent(
    Book book,
    String pageId,
    ExtractionConfig config,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      AnxLog.info('Extracting EPUB page content: $pageId');

      await _initializeWebView(book);

      // Navigate to specific page/CFI
      await _navigateToPage(pageId);

      // Extract content from current view
      final content = await _extractCurrentPageContent(book, pageId, config);

      return content;
    } catch (e) {
      AnxLog.severe('Failed to extract EPUB page content: $e');
      return null;
    } finally {
      await _disposeWebView();
    }
  }

  /// Get progress stream
  @override
  Stream<ExtractionProgress> getProgressStream() {
    return _progressController.stream;
  }

  /// Cancel ongoing extraction
  @override
  Future<void> cancelExtraction() async {
    _isExtracting = false;
    await _disposeWebView();
    AnxLog.info('EPUB extraction cancelled');
  }

  /// Initialize headless WebView for content extraction
  Future<void> _initializeWebView(Book book) async {
    try {
      final serverUrl = 'http://localhost:${Server().port}';
      final bookUrl =
          '$serverUrl/book/${Uri.encodeComponent(book.fileFullPath)}';
      final indexUrl = '$serverUrl/foliate-js/index.html';

      _headlessWebView = HeadlessInAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(indexUrl)),
        onWebViewCreated: (controller) {
          _webViewController = controller;
        },
        onLoadStop: (controller, url) async {
          // Initialize the book in the WebView
          await controller.evaluateJavascript(source: '''
            (async function() {
              try {
                if (typeof getView === 'function') {
                  const response = await fetch('$bookUrl');
                  const blob = await response.blob();
                  const file = new File([blob], '${book.title}.epub', { type: 'application/epub+zip' });
                  window.bookView = await getView(file);
                  return 'Book loaded successfully';
                } else {
                  throw new Error('getView function not available');
                }
              } catch (e) {
                console.error('Error loading book:', e);
                throw e;
              }
            })();
          ''');
        },
      );

      await _headlessWebView!.run();

      // Wait for book to load
      await Future<void>.delayed(const Duration(seconds: 3));

      AnxLog.info('WebView initialized for EPUB extraction');
    } catch (e) {
      AnxLog.severe('Failed to initialize WebView: $e');
      rethrow;
    }
  }

  /// Get book structure (chapters/sections)
  Future<List<Map<String, dynamic>>> _getBookStructure() async {
    if (_webViewController == null) return [];

    try {
      final result = await _webViewController!.evaluateJavascript(source: '''
        (function() {
          try {
            if (!window.bookView || !window.bookView.book) {
              return [];
            }
            
            const book = window.bookView.book;
            const sections = [];
            
            // Get sections from spine
            if (book.sections) {
              book.sections.forEach((section, index) => {
                sections.push({
                  id: section.id || index.toString(),
                  title: section.title || `Section ${index + 1}`,
                  index: index,
                  cfi: section.cfi || null,
                });
              });
            }
            
            return sections;
          } catch (e) {
            console.error('Error getting book structure:', e);
            return [];
          }
        })();
      ''');

      if (result is List) {
        return result.cast<Map<String, dynamic>>();
      }

      return [];
    } catch (e) {
      AnxLog.warning('Failed to get book structure: $e');
      return [];
    }
  }

  /// Extract content from a specific section
  Future<PageContent?> _extractSectionContent(
    Book book,
    Map<String, dynamic> section,
    int sectionIndex,
    ExtractionConfig config,
  ) async {
    if (_webViewController == null) return null;

    try {
      final sectionId = section['id']?.toString() ?? sectionIndex.toString();
      final sectionTitle = section['title']?.toString();

      // Navigate to section
      await _webViewController!.evaluateJavascript(source: '''
        (async function() {
          try {
            if (window.bookView && window.bookView.goTo) {
              await window.bookView.goTo($sectionIndex);
              // Wait for content to load
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          } catch (e) {
            console.error('Error navigating to section:', e);
          }
        })();
      ''');

      // Extract content from current view
      return await _extractCurrentPageContent(
        book,
        sectionId,
        config,
        chapterTitle: sectionTitle,
        pageNumber: sectionIndex,
      );
    } catch (e) {
      AnxLog.warning('Failed to extract section content: $e');
      return null;
    }
  }

  /// Extract content from current page view
  Future<PageContent?> _extractCurrentPageContent(
    Book book,
    String pageId,
    ExtractionConfig config, {
    String? chapterTitle,
    int? pageNumber,
  }) async {
    if (_webViewController == null) return null;

    try {
      final extractionStart = DateTime.now();

      // Extract text and HTML content
      final result = await _webViewController!.evaluateJavascript(source: '''
        (function() {
          try {
            const content = {
              text: '',
              html: '',
              elements: []
            };
            
            // Get the main content area
            const contentElements = document.querySelectorAll('p, div, h1, h2, h3, h4, h5, h6, span');
            const visibleElements = Array.from(contentElements).filter(el => {
              const rect = el.getBoundingClientRect();
              const style = window.getComputedStyle(el);
              return rect.height > 0 && 
                     style.display !== 'none' && 
                     style.visibility !== 'hidden' &&
                     el.textContent.trim().length > 0;
            });
            
            // Extract text content
            content.text = visibleElements.map(el => el.textContent).join('\\n\\n');
            
            // Extract HTML content if requested
            if (${config.includeHtml}) {
              content.html = visibleElements.map(el => el.outerHTML).join('\\n');
            }
            
            // Extract element information for segmentation
            visibleElements.forEach((el, index) => {
              content.elements.push({
                text: el.textContent,
                html: el.outerHTML,
                tagName: el.tagName.toLowerCase(),
                className: el.className,
                id: el.id,
                index: index
              });
            });
            
            return content;
          } catch (e) {
            console.error('Error extracting content:', e);
            return { text: '', html: '', elements: [] };
          }
        })();
      ''');

      if (result is! Map) return null;

      final contentMap = Map<String, dynamic>.from(result);
      final rawText = contentMap['text']?.toString() ?? '';
      final htmlContent = contentMap['html']?.toString();

      if (rawText.isEmpty) return null;

      // Clean and process text
      final cleanedText = _processingUtils.cleanText(rawText);
      final containsChinese = _processingUtils.containsChinese(cleanedText);
      final languages = _processingUtils.detectLanguages(cleanedText);
      final wordCount = _processingUtils.countWords(cleanedText);
      final paragraphCount = _processingUtils.countParagraphs(cleanedText);

      // Segment content if requested
      final segments = config.segmentContent
          ? _processingUtils.segmentContent(
              cleanedText,
              maxLength: config.maxSegmentLength,
              htmlContent: htmlContent,
            )
          : <ContentSegment>[];

      final processingTime =
          DateTime.now().difference(extractionStart).inMilliseconds;

      return PageContent(
        pageId: pageId,
        bookId: book.id,
        chapterTitle: chapterTitle,
        pageNumber: pageNumber,
        rawText: cleanedText,
        htmlContent: htmlContent,
        segments: segments,
        characterCount: cleanedText.length,
        wordCount: wordCount,
        paragraphCount: paragraphCount,
        containsChinese: containsChinese,
        languages: languages,
        format: BookFormat.epub,
        extractedAt: DateTime.now(),
        processingTimeMs: processingTime,
      );
    } catch (e) {
      AnxLog.warning('Failed to extract current page content: $e');
      return null;
    }
  }

  /// Navigate to a specific page/CFI
  Future<void> _navigateToPage(String pageId) async {
    if (_webViewController == null) return;

    try {
      await _webViewController!.evaluateJavascript(source: '''
        (async function() {
          try {
            if (window.bookView && window.bookView.goTo) {
              await window.bookView.goTo('$pageId');
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          } catch (e) {
            console.error('Error navigating to page:', e);
          }
        })();
      ''');
    } catch (e) {
      AnxLog.warning('Failed to navigate to page: $e');
    }
  }

  /// Dispose of WebView resources
  Future<void> _disposeWebView() async {
    try {
      await _headlessWebView?.dispose();
      _headlessWebView = null;
      _webViewController = null;
    } catch (e) {
      AnxLog.warning('Error disposing WebView: $e');
    }
  }

  /// Dispose of resources
  @override
  Future<void> dispose() async {
    _isExtracting = false;
    await _disposeWebView();
    await _progressController.close();
    AnxLog.info('EpubContentExtractor disposed');
  }
}
