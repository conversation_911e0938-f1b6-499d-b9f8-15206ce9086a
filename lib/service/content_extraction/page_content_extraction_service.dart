import 'dart:async';
import 'dart:math';

import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/service/content_extraction/epub_content_extractor.dart';
import 'package:dasso_reader/service/content_extraction/pdf_content_extractor.dart';
import 'package:dasso_reader/service/content_extraction/txt_content_extractor.dart';
import 'package:dasso_reader/service/content_extraction/content_processing_utils.dart';
import 'package:dasso_reader/service/cache/content_extraction_cache_service.dart';
import 'package:dasso_reader/service/content_extraction/content_extraction_error_handler.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Main service for extracting content from various book formats
class PageContentExtractionService {
  static final PageContentExtractionService _instance =
      PageContentExtractionService._internal();

  factory PageContentExtractionService() => _instance;

  PageContentExtractionService._internal();

  // Format-specific extractors
  late final EpubContentExtractor _epubExtractor;
  late final PdfContentExtractor _pdfExtractor;
  late final TxtContentExtractor _txtExtractor;

  // Utilities and cache
  late final ContentProcessingUtils _processingUtils;
  late final ContentExtractionCacheService _cacheService;
  late final ContentExtractionErrorHandler _errorHandler;

  bool _isInitialized = false;

  /// Initialize the service and its dependencies
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AnxLog.info('Initializing PageContentExtractionService...');

      // Initialize utilities first
      _processingUtils = ContentProcessingUtils();
      _cacheService = ContentExtractionCacheService();
      _errorHandler = ContentExtractionErrorHandler();
      await _cacheService.initialize();

      // Initialize format-specific extractors
      _epubExtractor = EpubContentExtractor(_processingUtils, _cacheService);
      _pdfExtractor = PdfContentExtractor(_processingUtils, _cacheService);
      _txtExtractor = TxtContentExtractor(_processingUtils, _cacheService);

      await _epubExtractor.initialize();
      await _pdfExtractor.initialize();
      await _txtExtractor.initialize();

      _isInitialized = true;
      AnxLog.info('PageContentExtractionService initialized successfully');
    } catch (e) {
      AnxLog.severe('Failed to initialize PageContentExtractionService: $e');
      rethrow;
    }
  }

  /// Extract content from a book
  Future<ExtractionResult> extractContent(
    Book book, {
    ExtractionConfig? config,
    Stream<ExtractionProgress>? progressStream,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final extractionConfig = config ?? const ExtractionConfig();
    final startTime = DateTime.now();

    try {
      AnxLog.info('Starting content extraction for book: ${book.title}');

      // Determine book format
      final format = _determineBookFormat(book.fileFullPath);

      // Check cache first if enabled
      if (extractionConfig.enableCaching) {
        final cachedResult = await _cacheService.getCachedExtraction(
          book.id,
          extractionConfig,
        );
        if (cachedResult != null) {
          AnxLog.info('Using cached extraction result for book: ${book.title}');
          return cachedResult;
        }
      }

      // Extract content using appropriate extractor
      final result = await _extractWithFormat(
        book,
        format,
        extractionConfig,
        progressStream,
        startTime,
      );

      // Cache the result if successful and caching is enabled
      if (result.success && extractionConfig.enableCaching) {
        await _cacheService.cacheExtraction(book.id, result, extractionConfig);
      }

      AnxLog.info(
        'Content extraction completed for book: ${book.title}. '
        'Success: ${result.success}, Pages: ${result.pages.length}',
      );

      return result;
    } catch (e) {
      AnxLog.severe('Content extraction failed for book: ${book.title}: $e');
      return ExtractionResult(
        success: false,
        errorMessage: e.toString(),
        startTime: startTime,
        endTime: DateTime.now(),
      );
    }
  }

  /// Extract content from a specific page or chapter
  Future<PageContent?> extractPageContent(
    Book book,
    String pageId, {
    ExtractionConfig? config,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final extractionConfig = config ?? const ExtractionConfig();

    try {
      AnxLog.info('Extracting page content: $pageId from book: ${book.title}');

      // Check cache first
      if (extractionConfig.enableCaching) {
        final cachedPage = await _cacheService.getCachedPageContent(
          book.id,
          pageId,
        );
        if (cachedPage != null) {
          return cachedPage;
        }
      }

      final format = _determineBookFormat(book.fileFullPath);
      final extractor = _getExtractorForFormat(format);

      final pageContent = await extractor.extractPageContent(
        book,
        pageId,
        extractionConfig,
      );

      // Cache the page content if successful
      if (pageContent != null && extractionConfig.enableCaching) {
        await _cacheService.cachePageContent(book.id, pageContent);
      }

      return pageContent;
    } catch (e) {
      AnxLog.severe('Failed to extract page content: $pageId: $e');
      return null;
    }
  }

  /// Get extraction progress for a book
  Stream<ExtractionProgress> getExtractionProgress(Book book) {
    final format = _determineBookFormat(book.fileFullPath);
    final extractor = _getExtractorForFormat(format);
    return extractor.getProgressStream();
  }

  /// Cancel ongoing extraction for a book
  Future<void> cancelExtraction(Book book) async {
    final format = _determineBookFormat(book.fileFullPath);
    final extractor = _getExtractorForFormat(format);
    await extractor.cancelExtraction();
  }

  /// Clear cached content for a book
  Future<void> clearCache(int bookId) async {
    await _cacheService.clearBookCache(bookId);
  }

  /// Clear all cached content
  Future<void> clearAllCache() async {
    await _cacheService.clearAllCache();
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStatistics() async {
    return await _cacheService.getStatistics();
  }

  /// Determine book format from file path
  BookFormat _determineBookFormat(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;

    switch (extension) {
      case 'epub':
        return BookFormat.epub;
      case 'pdf':
        return BookFormat.pdf;
      case 'txt':
        return BookFormat.txt;
      case 'mobi':
      case 'azw':
      case 'azw3':
        return BookFormat.mobi;
      case 'fb2':
        return BookFormat.fb2;
      case 'cbz':
      case 'cbr':
        return BookFormat.comic;
      default:
        AnxLog.warning('Unknown book format: $extension, defaulting to EPUB');
        return BookFormat.epub;
    }
  }

  /// Get the appropriate extractor for a book format
  dynamic _getExtractorForFormat(BookFormat format) {
    switch (format) {
      case BookFormat.epub:
        return _epubExtractor;
      case BookFormat.pdf:
        return _pdfExtractor;
      case BookFormat.txt:
        return _txtExtractor;
      case BookFormat.mobi:
      case BookFormat.fb2:
      case BookFormat.comic:
        // For now, use EPUB extractor as fallback
        // TODO: Implement specific extractors for these formats
        AnxLog.warning('Using EPUB extractor as fallback for format: $format');
        return _epubExtractor;
    }
  }

  /// Extract content using the appropriate format extractor
  Future<ExtractionResult> _extractWithFormat(
    Book book,
    BookFormat format,
    ExtractionConfig config,
    Stream<ExtractionProgress>? progressStream,
    DateTime startTime,
  ) async {
    final extractor = _getExtractorForFormat(format);

    return await extractor.extractContent(
      book,
      config,
      progressStream,
      startTime,
    );
  }

  /// Dispose of resources
  Future<void> dispose() async {
    if (!_isInitialized) return;

    try {
      await _epubExtractor.dispose();
      await _pdfExtractor.dispose();
      await _txtExtractor.dispose();
      await _cacheService.dispose();

      _isInitialized = false;
      AnxLog.info('PageContentExtractionService disposed');
    } catch (e) {
      AnxLog.warning('Error disposing PageContentExtractionService: $e');
    }
  }
}
