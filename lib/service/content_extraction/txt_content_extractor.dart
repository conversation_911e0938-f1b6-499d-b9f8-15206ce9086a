import 'dart:async';
import 'dart:io';

import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/service/content_extraction/content_processing_utils.dart';
import 'package:dasso_reader/service/cache/content_extraction_cache_service.dart';
import 'package:dasso_reader/service/content_extraction/base_content_extractor.dart';
import 'package:dasso_reader/service/convert_to_epub/txt/convert_from_txt.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Extractor for plain text content
class TxtContentExtractor implements BaseContentExtractor {
  final ContentProcessingUtils _processingUtils;
  final ContentExtractionCacheService _cacheService;

  bool _isInitialized = false;
  bool _isExtracting = false;

  final StreamController<ExtractionProgress> _progressController =
      StreamController<ExtractionProgress>.broadcast();

  TxtContentExtractor(this._processingUtils, this._cacheService);

  /// Initialize the TXT extractor
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AnxLog.info('Initializing TxtContentExtractor...');
      _isInitialized = true;
      AnxLog.info('TxtContentExtractor initialized');
    } catch (e) {
      AnxLog.severe('Failed to initialize TxtContentExtractor: $e');
      rethrow;
    }
  }

  /// Extract content from a TXT book
  @override
  Future<ExtractionResult> extractContent(
    Book book,
    ExtractionConfig config,
    Stream<ExtractionProgress>? progressStream,
    DateTime startTime,
  ) async {
    if (!_isInitialized) await initialize();
    if (_isExtracting) {
      throw StateError('Extraction already in progress');
    }

    _isExtracting = true;
    final pages = <PageContent>[];
    final warnings = <String>[];

    try {
      AnxLog.info('Starting TXT content extraction for: ${book.title}');

      // Read the text file
      final file = File(book.fileFullPath);
      if (!await file.exists()) {
        return ExtractionResult(
          success: false,
          errorMessage: 'Text file not found: ${book.fileFullPath}',
          startTime: startTime,
          endTime: DateTime.now(),
        );
      }

      // Read file content with proper encoding detection
      final rawContent = readFileWithEncoding(file);

      if (rawContent.isEmpty) {
        warnings.add('Text file is empty');
        return ExtractionResult(
          success: false,
          errorMessage: 'Text file is empty',
          warnings: warnings,
          startTime: startTime,
          endTime: DateTime.now(),
        );
      }

      // Progress update
      _progressController.add(const ExtractionProgress(
        currentPage: 1,
        totalPages: 1,
        operation: 'Processing text content...',
        percentage: 50,
      ));

      // Split content into chapters/sections if possible
      final sections = _splitIntoSections(rawContent);

      // Extract content from each section
      for (int i = 0; i < sections.length; i++) {
        if (!_isExtracting) break; // Check for cancellation

        final section = sections[i];
        final progress = ExtractionProgress(
          currentPage: i + 1,
          totalPages: sections.length,
          operation: 'Processing section: ${section['title']}',
          percentage: ((i + 1) / sections.length) * 100,
        );

        _progressController.add(progress);

        try {
          final pageContent = await _extractSectionContent(
            book,
            section,
            i,
            config,
          );

          if (pageContent != null) {
            pages.add(pageContent);
          }
        } catch (e) {
          warnings.add('Failed to extract section ${i + 1}: $e');
          AnxLog.warning('Failed to extract section ${i + 1}: $e');
        }
      }

      final endTime = DateTime.now();
      final processingTime = endTime.difference(startTime).inMilliseconds;

      // Calculate statistics
      final statistics = _processingUtils.calculateStatistics(pages);

      final result = ExtractionResult(
        success: pages.isNotEmpty,
        pages: pages,
        totalPages: pages.length,
        totalProcessingTimeMs: processingTime,
        warnings: warnings,
        statistics: statistics,
        startTime: startTime,
        endTime: endTime,
      );

      AnxLog.info(
        'TXT extraction completed: ${pages.length} sections extracted '
        'in ${processingTime}ms',
      );

      return result;
    } catch (e) {
      AnxLog.severe('TXT content extraction failed: $e');
      return ExtractionResult(
        success: false,
        errorMessage: e.toString(),
        warnings: warnings,
        startTime: startTime,
        endTime: DateTime.now(),
      );
    } finally {
      _isExtracting = false;
    }
  }

  /// Extract content from a specific page/section
  @override
  Future<PageContent?> extractPageContent(
    Book book,
    String pageId,
    ExtractionConfig config,
  ) async {
    if (!_isInitialized) await initialize();

    try {
      AnxLog.info('Extracting TXT page content: $pageId');

      // Read the text file
      final file = File(book.fileFullPath);
      if (!await file.exists()) {
        AnxLog.warning('Text file not found: ${book.fileFullPath}');
        return null;
      }

      final rawContent = readFileWithEncoding(file);
      final sections = _splitIntoSections(rawContent);

      // Find the requested section
      final sectionIndex = int.tryParse(pageId);
      if (sectionIndex == null || sectionIndex >= sections.length) {
        AnxLog.warning('Invalid section index: $pageId');
        return null;
      }

      final section = sections[sectionIndex];
      return await _extractSectionContent(book, section, sectionIndex, config);
    } catch (e) {
      AnxLog.severe('Failed to extract TXT page content: $e');
      return null;
    }
  }

  /// Get progress stream
  @override
  Stream<ExtractionProgress> getProgressStream() {
    return _progressController.stream;
  }

  /// Cancel ongoing extraction
  @override
  Future<void> cancelExtraction() async {
    _isExtracting = false;
    AnxLog.info('TXT extraction cancelled');
  }

  /// Split text content into sections/chapters
  List<Map<String, dynamic>> _splitIntoSections(String content) {
    final sections = <Map<String, dynamic>>[];

    try {
      // Use the same pattern as the TXT to EPUB converter
      final patternStr = RegExp(
        r'^(?:(.+ +)|())(第[一二三四五六七八九十零〇百千万两0123456789]+[章卷]|卷[一二三四五六七八九十零〇百千万两0123456789]+|chap(?:ter)\.?|vol(?:ume)?\.?|book|bk)(?:(?: +.+)?|(?:\S.*)?)$',
        multiLine: true,
        caseSensitive: false,
      );

      final matches = patternStr.allMatches(content).toList();

      if (matches.isEmpty) {
        // No chapter markers found, split by length
        const maxSectionLength = 20000;
        if (content.length <= maxSectionLength) {
          sections.add({
            'title': 'Full Text',
            'content': content,
            'startPos': 0,
            'endPos': content.length,
          });
        } else {
          var startIndex = 0;
          var sectionNumber = 1;

          while (startIndex < content.length) {
            final endIndex = startIndex + maxSectionLength;
            if (endIndex >= content.length) {
              sections.add({
                'title': 'Section $sectionNumber',
                'content': content.substring(startIndex),
                'startPos': startIndex,
                'endPos': content.length,
              });
              break;
            }

            final nextNewline = content.indexOf('\n', endIndex);
            final sectionEndIndex =
                nextNewline == -1 ? content.length : nextNewline;

            sections.add({
              'title': 'Section $sectionNumber',
              'content': content.substring(startIndex, sectionEndIndex),
              'startPos': startIndex,
              'endPos': sectionEndIndex,
            });

            startIndex = sectionEndIndex + 1;
            sectionNumber++;
          }
        }
      } else {
        // Extract chapters based on matches
        for (int i = 0; i < matches.length; i++) {
          final match = matches[i];
          final title = match.group(0)!;

          final startPos = match.start;
          final endPos =
              i < matches.length - 1 ? matches[i + 1].start : content.length;

          final fullContent = content.substring(startPos, endPos);
          final contentWithoutTitle =
              fullContent.substring(title.length).trim();

          sections.add({
            'title': title.trim(),
            'content': contentWithoutTitle,
            'startPos': startPos,
            'endPos': endPos,
          });
        }
      }
    } catch (e) {
      AnxLog.warning('Error splitting text into sections: $e');
      // Fallback: return entire content as single section
      sections.add({
        'title': 'Full Text',
        'content': content,
        'startPos': 0,
        'endPos': content.length,
      });
    }

    return sections;
  }

  /// Extract content from a specific section
  Future<PageContent?> _extractSectionContent(
    Book book,
    Map<String, dynamic> section,
    int sectionIndex,
    ExtractionConfig config,
  ) async {
    try {
      final extractionStart = DateTime.now();

      final title =
          section['title']?.toString() ?? 'Section ${sectionIndex + 1}';
      final rawText = section['content']?.toString() ?? '';

      if (rawText.isEmpty) return null;

      // Clean and process text
      final cleanedText = _processingUtils.cleanText(rawText);
      final containsChinese = _processingUtils.containsChinese(cleanedText);
      final languages = _processingUtils.detectLanguages(cleanedText);
      final wordCount = _processingUtils.countWords(cleanedText);
      final paragraphCount = _processingUtils.countParagraphs(cleanedText);

      // Segment content if requested
      final segments = config.segmentContent
          ? _processingUtils.segmentContent(
              cleanedText,
              maxLength: config.maxSegmentLength,
            )
          : <ContentSegment>[];

      final processingTime =
          DateTime.now().difference(extractionStart).inMilliseconds;

      return PageContent(
        pageId: sectionIndex.toString(),
        bookId: book.id,
        chapterTitle: title,
        pageNumber: sectionIndex,
        rawText: cleanedText,
        segments: segments,
        characterCount: cleanedText.length,
        wordCount: wordCount,
        paragraphCount: paragraphCount,
        containsChinese: containsChinese,
        languages: languages,
        format: BookFormat.txt,
        extractedAt: DateTime.now(),
        processingTimeMs: processingTime,
        metadata: {
          'startPosition': section['startPos'],
          'endPosition': section['endPos'],
        },
      );
    } catch (e) {
      AnxLog.warning('Failed to extract section content: $e');
      return null;
    }
  }

  /// Dispose of resources
  @override
  Future<void> dispose() async {
    _isExtracting = false;
    await _progressController.close();
    AnxLog.info('TxtContentExtractor disposed');
  }
}
