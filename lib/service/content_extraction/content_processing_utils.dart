import 'dart:math';

import 'package:dasso_reader/models/page_content.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Utility class for processing extracted content
class ContentProcessingUtils {
  // Regular expressions for text processing
  static final RegExp _chineseCharRegex = RegExp(r'[\u4e00-\u9fa5]');
  static final RegExp _whitespaceRegex = RegExp(r'\s+');
  static final RegExp _paragraphSeparatorRegex = RegExp(r'\n\s*\n');
  static final RegExp _htmlTagRegex = RegExp(r'<[^>]*>');
  static final RegExp _wordBoundaryRegex = RegExp(r'\b\w+\b');

  /// Clean raw text content by removing extra whitespace and formatting
  String cleanText(String rawText) {
    if (rawText.isEmpty) return rawText;

    try {
      // Remove HTML tags if present
      String cleaned = rawText.replaceAll(_htmlTagRegex, ' ');
      
      // Normalize whitespace
      cleaned = cleaned.replaceAll(_whitespaceRegex, ' ');
      
      // Remove leading/trailing whitespace
      cleaned = cleaned.trim();
      
      return cleaned;
    } catch (e) {
      AnxLog.warning('Error cleaning text: $e');
      return rawText;
    }
  }

  /// Strip HTML tags from content while preserving text
  String stripHtml(String htmlContent) {
    if (htmlContent.isEmpty) return htmlContent;
    
    try {
      return htmlContent.replaceAll(_htmlTagRegex, '').trim();
    } catch (e) {
      AnxLog.warning('Error stripping HTML: $e');
      return htmlContent;
    }
  }

  /// Detect if text contains Chinese characters
  bool containsChinese(String text) {
    return _chineseCharRegex.hasMatch(text);
  }

  /// Count Chinese characters in text
  int countChineseCharacters(String text) {
    return _chineseCharRegex.allMatches(text).length;
  }

  /// Detect primary language of text content
  List<String> detectLanguages(String text) {
    final languages = <String>[];
    
    if (text.isEmpty) return languages;

    try {
      // Check for Chinese characters
      if (containsChinese(text)) {
        languages.add('zh');
      }
      
      // Check for English/Latin characters
      if (RegExp(r'[a-zA-Z]').hasMatch(text)) {
        languages.add('en');
      }
      
      // Check for Japanese characters (Hiragana/Katakana)
      if (RegExp(r'[\u3040-\u309f\u30a0-\u30ff]').hasMatch(text)) {
        languages.add('ja');
      }
      
      // Check for Korean characters
      if (RegExp(r'[\uac00-\ud7af]').hasMatch(text)) {
        languages.add('ko');
      }
      
      // Default to English if no specific language detected
      if (languages.isEmpty && text.trim().isNotEmpty) {
        languages.add('en');
      }
    } catch (e) {
      AnxLog.warning('Error detecting languages: $e');
    }

    return languages;
  }

  /// Count words in text (approximate)
  int countWords(String text) {
    if (text.isEmpty) return 0;

    try {
      // For Chinese text, count characters as words
      if (containsChinese(text)) {
        return countChineseCharacters(text);
      }
      
      // For other languages, count word boundaries
      return _wordBoundaryRegex.allMatches(text).length;
    } catch (e) {
      AnxLog.warning('Error counting words: $e');
      return 0;
    }
  }

  /// Count paragraphs in text
  int countParagraphs(String text) {
    if (text.isEmpty) return 0;

    try {
      final paragraphs = text.split(_paragraphSeparatorRegex);
      return paragraphs.where((p) => p.trim().isNotEmpty).length;
    } catch (e) {
      AnxLog.warning('Error counting paragraphs: $e');
      return 1;
    }
  }

  /// Segment text into smaller chunks
  List<ContentSegment> segmentContent(
    String text, {
    int maxLength = 1000,
    String? htmlContent,
    String? cssSelector,
  }) {
    if (text.isEmpty) return [];

    final segments = <ContentSegment>[];
    
    try {
      // If text is shorter than max length, return as single segment
      if (text.length <= maxLength) {
        segments.add(ContentSegment(
          id: _generateSegmentId(),
          text: text,
          startPosition: 0,
          endPosition: text.length,
          containsChinese: containsChinese(text),
          htmlContent: htmlContent,
          cssSelector: cssSelector,
        ));
        return segments;
      }

      // Split by paragraphs first
      final paragraphs = text.split(_paragraphSeparatorRegex);
      int currentPosition = 0;
      String currentSegment = '';
      int segmentStart = 0;

      for (int i = 0; i < paragraphs.length; i++) {
        final paragraph = paragraphs[i].trim();
        if (paragraph.isEmpty) continue;

        // Check if adding this paragraph would exceed max length
        if (currentSegment.isNotEmpty && 
            (currentSegment.length + paragraph.length + 2) > maxLength) {
          // Create segment with current content
          segments.add(ContentSegment(
            id: _generateSegmentId(),
            text: currentSegment.trim(),
            startPosition: segmentStart,
            endPosition: currentPosition,
            containsChinese: containsChinese(currentSegment),
            htmlContent: htmlContent,
            cssSelector: cssSelector,
          ));

          // Start new segment
          currentSegment = paragraph;
          segmentStart = currentPosition;
        } else {
          // Add paragraph to current segment
          if (currentSegment.isNotEmpty) {
            currentSegment += '\n\n';
            currentPosition += 2;
          }
          currentSegment += paragraph;
        }
        
        currentPosition += paragraph.length;
      }

      // Add final segment if not empty
      if (currentSegment.trim().isNotEmpty) {
        segments.add(ContentSegment(
          id: _generateSegmentId(),
          text: currentSegment.trim(),
          startPosition: segmentStart,
          endPosition: currentPosition,
          containsChinese: containsChinese(currentSegment),
          htmlContent: htmlContent,
          cssSelector: cssSelector,
        ));
      }
    } catch (e) {
      AnxLog.warning('Error segmenting content: $e');
      // Fallback: return original text as single segment
      segments.add(ContentSegment(
        id: _generateSegmentId(),
        text: text,
        startPosition: 0,
        endPosition: text.length,
        containsChinese: containsChinese(text),
        htmlContent: htmlContent,
        cssSelector: cssSelector,
      ));
    }

    return segments;
  }

  /// Extract text content from HTML
  String extractTextFromHtml(String htmlContent) {
    if (htmlContent.isEmpty) return '';

    try {
      // Remove script and style tags completely
      String cleaned = htmlContent.replaceAll(
        RegExp(r'<(script|style)[^>]*>.*?</\1>', caseSensitive: false, dotAll: true),
        '',
      );
      
      // Replace block elements with line breaks
      cleaned = cleaned.replaceAll(
        RegExp(r'</(div|p|h[1-6]|li|br)[^>]*>', caseSensitive: false),
        '\n',
      );
      
      // Remove all remaining HTML tags
      cleaned = stripHtml(cleaned);
      
      // Clean up whitespace
      return cleanText(cleaned);
    } catch (e) {
      AnxLog.warning('Error extracting text from HTML: $e');
      return stripHtml(htmlContent);
    }
  }

  /// Generate a unique segment ID
  String _generateSegmentId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(10000);
    return 'seg_${timestamp}_$random';
  }

  /// Validate extracted content
  bool validateContent(PageContent content) {
    try {
      // Check required fields
      if (content.pageId.isEmpty || content.bookId <= 0) {
        return false;
      }
      
      // Check text content
      if (content.rawText.isEmpty && content.segments.isEmpty) {
        return false;
      }
      
      // Validate segments
      for (final segment in content.segments) {
        if (segment.text.isEmpty || 
            segment.startPosition < 0 || 
            segment.endPosition <= segment.startPosition) {
          return false;
        }
      }
      
      return true;
    } catch (e) {
      AnxLog.warning('Error validating content: $e');
      return false;
    }
  }

  /// Calculate content statistics
  Map<String, dynamic> calculateStatistics(List<PageContent> pages) {
    if (pages.isEmpty) {
      return {
        'totalPages': 0,
        'totalCharacters': 0,
        'totalWords': 0,
        'totalParagraphs': 0,
        'chinesePages': 0,
        'averageCharactersPerPage': 0.0,
        'averageWordsPerPage': 0.0,
        'languages': <String>[],
      };
    }

    try {
      final totalCharacters = pages.fold<int>(0, (sum, page) => sum + page.characterCount);
      final totalWords = pages.fold<int>(0, (sum, page) => sum + page.wordCount);
      final totalParagraphs = pages.fold<int>(0, (sum, page) => sum + page.paragraphCount);
      final chinesePages = pages.where((page) => page.containsChinese).length;
      
      final allLanguages = <String>{};
      for (final page in pages) {
        allLanguages.addAll(page.languages);
      }

      return {
        'totalPages': pages.length,
        'totalCharacters': totalCharacters,
        'totalWords': totalWords,
        'totalParagraphs': totalParagraphs,
        'chinesePages': chinesePages,
        'averageCharactersPerPage': totalCharacters / pages.length,
        'averageWordsPerPage': totalWords / pages.length,
        'languages': allLanguages.toList(),
      };
    } catch (e) {
      AnxLog.warning('Error calculating statistics: $e');
      return {'error': e.toString()};
    }
  }
}
