import 'package:freezed_annotation/freezed_annotation.dart';

part 'page_content.freezed.dart';
part 'page_content.g.dart';

/// Represents the type of content extracted from a page
enum ContentType {
  /// Plain text content
  text,
  /// HTML content with markup
  html,
  /// Image content
  image,
  /// Mixed content (text + images)
  mixed,
}

/// Represents the format of the source book
enum BookFormat {
  /// EPUB format
  epub,
  /// PDF format
  pdf,
  /// Plain text format
  txt,
  /// MOBI format
  mobi,
  /// FB2 format
  fb2,
  /// Comic book format (CBZ/CBR)
  comic,
}

/// Represents a segment of content within a page
@freezed
class ContentSegment with _$ContentSegment {
  const factory ContentSegment({
    /// Unique identifier for this segment
    required String id,
    /// The actual text content
    required String text,
    /// Type of content in this segment
    @Default(ContentType.text) ContentType type,
    /// Start position in the original content
    required int startPosition,
    /// End position in the original content
    required int endPosition,
    /// Language of the content (e.g., 'zh', 'en')
    String? language,
    /// Whether this segment contains Chinese characters
    @Default(false) bool containsChinese,
    /// HTML markup if available
    String? htmlContent,
    /// CSS selector path for this segment
    String? cssSelector,
    /// Additional metadata
    @Default({}) Map<String, dynamic> metadata,
  }) = _ContentSegment;

  factory ContentSegment.fromJson(Map<String, dynamic> json) =>
      _$ContentSegmentFromJson(json);
}

/// Represents extracted content from a single page
@freezed
class PageContent with _$PageContent {
  const factory PageContent({
    /// Unique identifier for this page
    required String pageId,
    /// Book ID this page belongs to
    required int bookId,
    /// Chapter or section identifier
    String? chapterId,
    /// Chapter title
    String? chapterTitle,
    /// Page number or index
    int? pageNumber,
    /// CFI (Canonical Fragment Identifier) for EPUB
    String? cfi,
    /// Raw text content of the page
    required String rawText,
    /// HTML content if available
    String? htmlContent,
    /// List of content segments
    @Default([]) List<ContentSegment> segments,
    /// Total character count
    required int characterCount,
    /// Word count (approximate)
    required int wordCount,
    /// Paragraph count
    @Default(0) int paragraphCount,
    /// Whether the page contains Chinese text
    @Default(false) bool containsChinese,
    /// Detected language(s)
    @Default([]) List<String> languages,
    /// Source book format
    required BookFormat format,
    /// Extraction timestamp
    required DateTime extractedAt,
    /// Processing time in milliseconds
    @Default(0) int processingTimeMs,
    /// Additional metadata
    @Default({}) Map<String, dynamic> metadata,
  }) = _PageContent;

  factory PageContent.fromJson(Map<String, dynamic> json) =>
      _$PageContentFromJson(json);
}

/// Represents the result of a content extraction operation
@freezed
class ExtractionResult with _$ExtractionResult {
  const factory ExtractionResult({
    /// Whether the extraction was successful
    required bool success,
    /// List of extracted page contents
    @Default([]) List<PageContent> pages,
    /// Total pages processed
    @Default(0) int totalPages,
    /// Total processing time in milliseconds
    @Default(0) int totalProcessingTimeMs,
    /// Error message if extraction failed
    String? errorMessage,
    /// Warnings encountered during extraction
    @Default([]) List<String> warnings,
    /// Extraction statistics
    @Default({}) Map<String, dynamic> statistics,
    /// Timestamp when extraction started
    required DateTime startTime,
    /// Timestamp when extraction completed
    DateTime? endTime,
  }) = _ExtractionResult;

  factory ExtractionResult.fromJson(Map<String, dynamic> json) =>
      _$ExtractionResultFromJson(json);
}

/// Configuration for content extraction
@freezed
class ExtractionConfig with _$ExtractionConfig {
  const factory ExtractionConfig({
    /// Whether to extract HTML content
    @Default(true) bool includeHtml,
    /// Whether to segment content into smaller parts
    @Default(true) bool segmentContent,
    /// Maximum segment length in characters
    @Default(1000) int maxSegmentLength,
    /// Whether to detect Chinese text
    @Default(true) bool detectChinese,
    /// Whether to detect languages
    @Default(true) bool detectLanguages,
    /// Whether to extract images
    @Default(false) bool includeImages,
    /// Whether to preserve formatting
    @Default(true) bool preserveFormatting,
    /// Custom CSS selectors to extract
    @Default([]) List<String> customSelectors,
    /// Elements to exclude from extraction
    @Default(['script', 'style', 'nav', 'header', 'footer']) List<String> excludeElements,
    /// Whether to cache results
    @Default(true) bool enableCaching,
    /// Cache duration in hours
    @Default(24) int cacheDurationHours,
  }) = _ExtractionConfig;

  factory ExtractionConfig.fromJson(Map<String, dynamic> json) =>
      _$ExtractionConfigFromJson(json);
}

/// Represents extraction progress
@freezed
class ExtractionProgress with _$ExtractionProgress {
  const factory ExtractionProgress({
    /// Current page being processed
    required int currentPage,
    /// Total pages to process
    required int totalPages,
    /// Current operation description
    required String operation,
    /// Progress percentage (0-100)
    required double percentage,
    /// Estimated time remaining in seconds
    int? estimatedTimeRemaining,
    /// Pages processed per second
    @Default(0.0) double pagesPerSecond,
  }) = _ExtractionProgress;

  factory ExtractionProgress.fromJson(Map<String, dynamic> json) =>
      _$ExtractionProgressFromJson(json);
}
